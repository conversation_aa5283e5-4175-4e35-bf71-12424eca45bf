{"confusion_matrix": [[9402, 43, 0, 79, 0, 56, 20, 72, 23, 0, 38, 2, 37, 37, 0, 63, 4, 45, 1, 21, 5, 2, 36, 8, 0, 6], [142, 6032, 0, 27, 0, 1265, 52, 23, 13, 0, 406, 123, 66, 17, 0, 523, 183, 101, 2, 257, 93, 3, 390, 164, 0, 118], [0, 0, 9766, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 17, 0, 0, 79, 0, 0, 124, 0], [46, 2, 0, 7913, 0, 4, 175, 4, 759, 0, 3, 25, 28, 857, 0, 6, 28, 41, 0, 2, 32, 0, 9, 32, 0, 34], [0, 0, 0, 0, 9998, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [123, 1317, 0, 36, 0, 5925, 77, 35, 14, 0, 430, 124, 74, 17, 2, 538, 219, 111, 4, 269, 84, 5, 374, 130, 0, 92], [27, 30, 0, 320, 1, 27, 7594, 5, 247, 0, 27, 205, 36, 342, 0, 52, 277, 48, 0, 19, 180, 0, 41, 266, 0, 256], [28, 6, 0, 5, 0, 5, 1, 9732, 3, 0, 5, 0, 146, 2, 0, 3, 0, 57, 0, 2, 1, 1, 0, 1, 0, 2], [16, 0, 0, 789, 0, 3, 118, 7, 7559, 0, 1, 14, 23, 1332, 0, 6, 27, 32, 0, 1, 25, 0, 3, 20, 0, 24], [0, 0, 0, 0, 0, 1, 0, 0, 0, 9994, 0, 0, 0, 0, 2, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 0], [74, 308, 0, 34, 0, 452, 82, 9, 15, 1, 5269, 228, 46, 22, 3, 974, 363, 126, 1, 679, 149, 0, 742, 262, 0, 161], [9, 92, 0, 99, 0, 97, 410, 10, 79, 0, 177, 4944, 36, 71, 0, 222, 1251, 76, 0, 113, 784, 0, 130, 722, 0, 678], [27, 10, 0, 32, 0, 12, 10, 389, 25, 0, 9, 8, 8110, 25, 0, 14, 10, 1284, 1, 3, 7, 2, 6, 8, 0, 8], [18, 1, 0, 740, 0, 1, 99, 4, 1543, 0, 0, 22, 6, 7452, 0, 2, 24, 28, 0, 0, 8, 0, 7, 19, 0, 26], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9515, 0, 0, 0, 65, 0, 0, 348, 0, 0, 72, 0], [67, 282, 1, 38, 0, 309, 86, 12, 17, 0, 1019, 187, 51, 22, 0, 5259, 358, 94, 3, 747, 165, 2, 828, 258, 0, 195], [11, 69, 0, 96, 0, 115, 294, 4, 58, 0, 145, 631, 30, 113, 0, 255, 5440, 60, 0, 115, 829, 0, 147, 900, 0, 688], [19, 15, 0, 41, 0, 12, 10, 161, 38, 1, 11, 14, 998, 44, 0, 21, 19, 8545, 0, 3, 5, 2, 12, 14, 0, 15], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 9763, 0, 0, 68, 0, 0, 124, 0], [85, 294, 0, 51, 0, 401, 72, 9, 19, 1, 1167, 193, 55, 21, 1, 1120, 344, 133, 5, 4610, 135, 1, 837, 261, 0, 185], [6, 52, 0, 119, 0, 65, 321, 3, 92, 0, 96, 530, 29, 152, 0, 196, 1030, 79, 0, 76, 5121, 0, 99, 952, 0, 982], [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 65, 0, 0, 0, 57, 0, 0, 8671, 0, 0, 1205, 0], [93, 269, 0, 27, 0, 358, 57, 16, 17, 1, 1059, 182, 59, 27, 1, 1178, 309, 147, 7, 764, 145, 4, 4833, 252, 2, 193], [10, 79, 0, 87, 1, 99, 313, 5, 73, 0, 126, 486, 47, 124, 0, 206, 1115, 65, 0, 80, 1019, 0, 140, 4939, 0, 986], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 0, 130, 0, 0, 1761, 0, 0, 8058, 0], [3, 47, 0, 96, 0, 57, 329, 5, 128, 0, 88, 469, 37, 174, 0, 188, 837, 78, 0, 80, 1089, 0, 140, 923, 0, 5232]], "class_names": ["BPSK", "QPSK", "8PSK", "16PSK", "32PSK", "64PSK", "4QAM", "8QAM", "16QAM", "32QAM", "64QAM", "128QAM", "256QAM", "2FSK", "4FSK", "8FSK", "16FSK", "4PAM", "8PAM", "16PAM", "AM-DSB", "AM-DSB-SC", "AM-USB", "AM-LSB", "FM", "PM"]}