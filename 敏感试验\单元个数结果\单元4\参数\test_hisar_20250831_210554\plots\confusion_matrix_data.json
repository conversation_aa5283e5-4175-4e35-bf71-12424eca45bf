{"confusion_matrix": [[9588, 64, 1, 32, 1, 58, 13, 40, 3, 0, 28, 6, 17, 14, 1, 49, 2, 20, 0, 33, 3, 0, 23, 1, 0, 3], [112, 6895, 0, 7, 0, 1147, 37, 12, 2, 2, 348, 127, 17, 6, 0, 352, 63, 35, 0, 282, 86, 0, 263, 120, 0, 87], [2, 0, 9926, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 10, 0, 0, 0, 3, 0, 0, 28, 0, 0, 30, 0], [104, 13, 0, 8119, 0, 12, 300, 8, 398, 0, 10, 60, 23, 743, 0, 10, 20, 28, 0, 8, 68, 0, 2, 43, 0, 31], [0, 0, 0, 0, 10000, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [108, 1602, 0, 5, 0, 6415, 49, 15, 4, 2, 369, 94, 30, 3, 0, 358, 95, 48, 0, 298, 130, 0, 213, 74, 0, 88], [35, 46, 0, 132, 0, 60, 7974, 4, 66, 0, 48, 305, 18, 165, 0, 53, 161, 20, 0, 40, 344, 0, 32, 255, 0, 242], [27, 2, 2, 0, 0, 12, 0, 9774, 0, 0, 4, 3, 125, 0, 0, 2, 0, 42, 0, 2, 1, 0, 4, 0, 0, 0], [42, 3, 0, 1062, 0, 4, 238, 3, 6871, 0, 2, 27, 27, 1507, 0, 4, 35, 26, 0, 7, 84, 0, 5, 25, 0, 28], [0, 0, 0, 0, 0, 0, 0, 0, 0, 9999, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [78, 494, 1, 7, 0, 445, 56, 8, 5, 0, 5535, 177, 17, 5, 0, 1064, 161, 36, 0, 668, 227, 0, 718, 170, 0, 128], [12, 145, 0, 37, 0, 125, 340, 8, 8, 0, 213, 6025, 23, 20, 0, 243, 546, 41, 0, 159, 770, 0, 133, 604, 0, 548], [36, 21, 0, 10, 0, 26, 9, 303, 3, 0, 6, 11, 8495, 13, 0, 14, 6, 989, 0, 8, 17, 0, 11, 15, 0, 7], [27, 2, 0, 706, 1, 3, 195, 1, 798, 0, 5, 21, 8, 8039, 0, 6, 29, 13, 0, 6, 60, 0, 3, 40, 0, 37], [0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 9877, 0, 0, 0, 34, 0, 0, 67, 0, 0, 19, 0], [54, 463, 0, 12, 0, 419, 68, 9, 2, 3, 880, 255, 20, 8, 0, 5643, 137, 43, 1, 654, 186, 0, 790, 229, 0, 124], [11, 112, 0, 18, 0, 167, 258, 4, 13, 0, 285, 1090, 13, 39, 0, 147, 4822, 32, 0, 199, 942, 0, 149, 1001, 0, 698], [20, 29, 2, 18, 0, 30, 16, 138, 8, 2, 25, 22, 1017, 18, 0, 22, 8, 8564, 1, 9, 14, 0, 14, 11, 0, 12], [0, 1, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 62, 0, 0, 0, 9880, 0, 0, 12, 0, 0, 41, 0], [68, 447, 0, 13, 0, 456, 60, 15, 1, 6, 976, 183, 25, 10, 0, 1162, 168, 50, 0, 5036, 229, 0, 856, 139, 0, 100], [10, 126, 0, 32, 0, 94, 326, 0, 20, 0, 162, 865, 20, 25, 0, 118, 552, 35, 0, 140, 5441, 0, 124, 908, 0, 1002], [0, 0, 20, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 219, 0, 0, 1, 45, 0, 0, 8429, 0, 0, 1283, 0], [88, 486, 0, 6, 0, 406, 56, 6, 4, 1, 904, 160, 23, 6, 1, 1227, 195, 72, 0, 1037, 201, 0, 4825, 155, 0, 141], [12, 147, 1, 32, 0, 155, 282, 3, 9, 3, 226, 763, 14, 28, 0, 162, 645, 33, 0, 126, 820, 0, 115, 5461, 0, 963], [0, 3, 23, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 148, 0, 0, 0, 114, 0, 0, 1209, 0, 0, 8502, 0], [7, 96, 0, 27, 0, 114, 331, 2, 15, 0, 170, 647, 14, 45, 0, 114, 562, 35, 0, 94, 1077, 0, 115, 1024, 0, 5511]], "class_names": ["BPSK", "QPSK", "8PSK", "16PSK", "32PSK", "64PSK", "4QAM", "8QAM", "16QAM", "32QAM", "64QAM", "128QAM", "256QAM", "2FSK", "4FSK", "8FSK", "16FSK", "4PAM", "8PAM", "16PAM", "AM-DSB", "AM-DSB-SC", "AM-USB", "AM-LSB", "FM", "PM"]}