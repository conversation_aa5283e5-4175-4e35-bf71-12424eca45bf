{"metadata": {"class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "dataset_type": "rml", "total_samples": 33000, "snr_list": [-20.0, -18.0, -16.0, -14.0, -12.0, -10.0, -8.0, -6.0, -4.0, -2.0, 0.0, 2.0, 4.0, 6.0, 8.0, 10.0, 12.0, 14.0, 16.0, 18.0], "num_classes": 11}, "confusion_matrices": {"SNR_-20dB": {"snr": -20.0, "confusion_matrix": [[1, 3, 139, 2, 0, 3, 1, 0, 0, 0, 1], [4, 5, 135, 0, 0, 1, 1, 0, 1, 0, 3], [1, 3, 142, 0, 0, 1, 1, 0, 0, 0, 2], [3, 2, 131, 1, 1, 7, 0, 1, 0, 2, 2], [2, 3, 133, 0, 1, 6, 1, 1, 0, 2, 1], [0, 3, 133, 2, 2, 6, 0, 1, 0, 1, 2], [1, 2, 137, 0, 0, 4, 0, 1, 0, 2, 3], [2, 4, 134, 0, 1, 5, 2, 0, 0, 1, 1], [6, 0, 134, 3, 0, 4, 2, 0, 0, 1, 0], [0, 0, 141, 1, 2, 0, 2, 0, 0, 1, 3], [5, 7, 124, 2, 1, 4, 2, 0, 0, 1, 4]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.757575757575758, "macro_f1": 3.5193497384977244, "kappa": 0.007333333333333303}, "sample_count": 1650}, "SNR_-18dB": {"snr": -18.0, "confusion_matrix": [[1, 5, 137, 0, 1, 2, 0, 1, 0, 0, 3], [2, 11, 127, 1, 1, 4, 0, 0, 0, 1, 3], [4, 5, 132, 1, 0, 3, 4, 0, 0, 0, 1], [2, 5, 127, 0, 1, 9, 1, 0, 0, 1, 4], [3, 5, 133, 3, 0, 3, 2, 0, 0, 0, 1], [2, 2, 131, 2, 2, 7, 2, 0, 0, 1, 1], [2, 3, 135, 1, 1, 2, 2, 2, 0, 0, 2], [3, 2, 138, 0, 1, 1, 0, 1, 0, 1, 3], [10, 7, 107, 4, 2, 11, 7, 0, 1, 1, 0], [2, 2, 139, 0, 1, 2, 1, 0, 0, 0, 3], [1, 7, 128, 4, 1, 2, 2, 0, 0, 0, 5]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.696969696969697, "macro_f1": 4.212449337476597, "kappa": 0.006666666666666932}, "sample_count": 1650}, "SNR_-16dB": {"snr": -16.0, "confusion_matrix": [[1, 3, 142, 0, 0, 3, 1, 0, 0, 0, 0], [1, 14, 118, 4, 1, 4, 0, 1, 0, 0, 7], [1, 5, 130, 3, 0, 7, 2, 0, 0, 0, 2], [2, 2, 142, 1, 0, 0, 0, 0, 1, 1, 1], [1, 1, 137, 1, 0, 6, 2, 0, 0, 0, 2], [4, 3, 135, 0, 1, 2, 0, 0, 1, 0, 4], [1, 2, 133, 0, 0, 5, 1, 0, 0, 3, 5], [3, 0, 131, 1, 0, 7, 4, 1, 0, 1, 2], [17, 1, 83, 9, 5, 12, 7, 5, 2, 7, 2], [0, 1, 137, 2, 0, 4, 2, 2, 0, 1, 1], [1, 8, 130, 1, 1, 2, 0, 0, 0, 0, 7]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.696969696969697, "macro_f1": 4.4978479862783365, "kappa": 0.00666666666666671}, "sample_count": 1650}, "SNR_-14dB": {"snr": -14.0, "confusion_matrix": [[1, 4, 134, 1, 0, 6, 2, 0, 0, 0, 2], [4, 16, 112, 1, 4, 3, 2, 0, 0, 0, 8], [2, 4, 140, 1, 0, 2, 1, 0, 0, 0, 0], [3, 4, 134, 1, 0, 2, 1, 0, 0, 2, 3], [2, 2, 131, 1, 1, 6, 2, 1, 0, 0, 4], [3, 6, 130, 0, 0, 5, 2, 1, 0, 0, 3], [5, 3, 121, 2, 1, 9, 3, 1, 0, 1, 4], [10, 1, 95, 6, 7, 11, 7, 3, 3, 3, 4], [33, 0, 13, 8, 15, 12, 4, 37, 18, 7, 3], [4, 2, 134, 1, 1, 3, 1, 0, 0, 1, 3], [4, 16, 112, 1, 1, 4, 1, 0, 0, 0, 11]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 12.121212121212121, "macro_f1": 7.567123151379178, "kappa": 0.033333333333333326}, "sample_count": 1650}, "SNR_-12dB": {"snr": -12.0, "confusion_matrix": [[2, 1, 126, 1, 1, 8, 6, 1, 0, 1, 3], [3, 38, 86, 0, 0, 4, 1, 0, 0, 0, 18], [0, 3, 139, 0, 0, 5, 1, 0, 0, 0, 2], [1, 3, 134, 2, 2, 2, 0, 0, 0, 0, 6], [1, 4, 131, 1, 1, 5, 2, 1, 1, 1, 2], [3, 6, 122, 0, 0, 9, 1, 2, 1, 1, 5], [8, 5, 90, 13, 2, 11, 17, 2, 1, 1, 0], [27, 1, 52, 8, 9, 11, 10, 12, 10, 6, 4], [37, 0, 3, 0, 8, 2, 3, 57, 29, 11, 0], [0, 2, 133, 2, 1, 5, 2, 0, 0, 0, 5], [0, 29, 89, 4, 0, 6, 1, 1, 0, 0, 20]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 16.3030303030303, "macro_f1": 13.077724411114788, "kappa": 0.07933333333333337}, "sample_count": 1650}, "SNR_-10dB": {"snr": -10.0, "confusion_matrix": [[4, 8, 118, 3, 1, 6, 4, 2, 0, 0, 4], [0, 49, 47, 1, 1, 7, 0, 0, 0, 0, 45], [1, 1, 138, 1, 1, 3, 0, 2, 0, 0, 3], [12, 3, 111, 5, 2, 9, 4, 3, 0, 0, 1], [8, 4, 115, 0, 3, 9, 3, 0, 1, 0, 7], [0, 11, 97, 1, 0, 27, 3, 1, 0, 1, 9], [9, 4, 15, 40, 1, 11, 56, 5, 6, 1, 2], [42, 0, 1, 6, 12, 9, 5, 35, 27, 13, 0], [14, 0, 1, 0, 1, 0, 0, 81, 51, 2, 0], [8, 3, 120, 2, 0, 4, 2, 1, 1, 3, 6], [1, 44, 49, 1, 0, 14, 1, 1, 0, 0, 39]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 24.848484848484848, "macro_f1": 22.484187579547203, "kappa": 0.17333333333333323}, "sample_count": 1650}, "SNR_-8dB": {"snr": -8.0, "confusion_matrix": [[16, 4, 84, 11, 6, 15, 3, 4, 2, 4, 1], [0, 78, 4, 0, 0, 11, 1, 0, 1, 0, 55], [4, 2, 136, 1, 0, 4, 1, 0, 1, 0, 1], [11, 1, 73, 17, 1, 8, 30, 1, 3, 2, 3], [19, 6, 69, 6, 8, 17, 9, 7, 2, 1, 6], [2, 13, 42, 4, 4, 63, 2, 2, 2, 0, 16], [0, 0, 2, 42, 1, 1, 100, 2, 2, 0, 0], [29, 0, 1, 0, 4, 1, 0, 71, 35, 9, 0], [5, 0, 1, 0, 0, 0, 1, 67, 76, 0, 0], [13, 0, 83, 7, 10, 8, 5, 8, 8, 3, 5], [1, 62, 4, 2, 0, 18, 1, 0, 0, 0, 62]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 38.18181818181819, "macro_f1": 34.540873173997255, "kappa": 0.31999999999999995}, "sample_count": 1650}, "SNR_-6dB": {"snr": -6.0, "confusion_matrix": [[35, 1, 17, 8, 19, 14, 11, 20, 8, 15, 2], [0, 108, 0, 0, 0, 1, 0, 0, 0, 0, 41], [1, 5, 138, 0, 1, 2, 1, 0, 0, 1, 1], [7, 6, 9, 65, 3, 6, 46, 4, 1, 3, 0], [23, 0, 10, 7, 41, 31, 5, 9, 11, 11, 2], [2, 10, 3, 1, 7, 108, 1, 1, 2, 2, 13], [0, 0, 0, 19, 0, 0, 128, 3, 0, 0, 0], [13, 0, 0, 0, 1, 3, 1, 72, 58, 2, 0], [6, 0, 1, 0, 0, 0, 0, 39, 103, 1, 0], [48, 3, 19, 10, 8, 12, 7, 16, 10, 12, 5], [0, 76, 0, 1, 2, 24, 0, 0, 0, 0, 47]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 51.93939393939394, "macro_f1": 48.928933013640204, "kappa": 0.4713333333333335}, "sample_count": 1650}, "SNR_-4dB": {"snr": -4.0, "confusion_matrix": [[70, 0, 1, 3, 20, 4, 0, 29, 8, 15, 0], [0, 132, 0, 0, 0, 0, 0, 0, 0, 0, 18], [0, 3, 141, 0, 0, 1, 0, 1, 0, 1, 3], [2, 0, 2, 110, 0, 0, 32, 1, 0, 2, 1], [21, 0, 0, 0, 95, 16, 0, 8, 3, 5, 2], [0, 2, 0, 1, 6, 129, 0, 0, 0, 0, 12], [0, 0, 1, 4, 0, 0, 145, 0, 0, 0, 0], [9, 0, 1, 0, 0, 0, 0, 89, 50, 1, 0], [2, 0, 2, 0, 0, 0, 0, 39, 107, 0, 0], [52, 0, 3, 2, 18, 2, 3, 37, 14, 19, 0], [0, 93, 0, 0, 2, 15, 0, 0, 0, 0, 40]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 65.27272727272727, "macro_f1": 63.161915499784996, "kappa": 0.6180000000000001}, "sample_count": 1650}, "SNR_-2dB": {"snr": -2.0, "confusion_matrix": [[84, 0, 2, 0, 9, 0, 0, 25, 7, 23, 0], [0, 130, 0, 0, 0, 0, 0, 0, 0, 0, 20], [2, 2, 138, 0, 0, 3, 0, 1, 0, 0, 4], [0, 0, 2, 132, 0, 0, 15, 1, 0, 0, 0], [2, 0, 0, 0, 145, 3, 0, 0, 0, 0, 0], [0, 0, 0, 0, 1, 138, 0, 0, 0, 0, 11], [0, 0, 0, 2, 0, 0, 148, 0, 0, 0, 0], [2, 0, 4, 0, 0, 0, 0, 115, 29, 0, 0], [0, 0, 2, 0, 0, 0, 0, 29, 119, 0, 0], [76, 0, 1, 1, 5, 0, 0, 17, 6, 44, 0], [0, 94, 0, 0, 1, 15, 0, 0, 0, 0, 40]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 74.72727272727273, "macro_f1": 73.18842278837955, "kappa": 0.722}, "sample_count": 1650}, "SNR_0dB": {"snr": 0.0, "confusion_matrix": [[131, 0, 0, 0, 0, 0, 0, 7, 5, 7, 0], [0, 145, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 3, 142, 0, 0, 1, 3, 0, 0, 1, 0], [1, 0, 2, 143, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 149, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 11], [0, 0, 2, 0, 0, 0, 148, 0, 0, 0, 0], [1, 0, 2, 0, 0, 0, 0, 127, 20, 0, 0], [1, 0, 1, 0, 0, 0, 0, 28, 120, 0, 0], [42, 0, 0, 0, 0, 0, 0, 4, 2, 102, 0], [0, 104, 0, 0, 0, 10, 0, 0, 0, 0, 36]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 83.75757575757575, "macro_f1": 82.67891521375624, "kappa": 0.8213333333333334}, "sample_count": 1650}, "SNR_2dB": {"snr": 2.0, "confusion_matrix": [[142, 0, 0, 0, 0, 1, 0, 2, 3, 2, 0], [0, 145, 0, 0, 0, 0, 0, 0, 0, 0, 5], [0, 4, 138, 1, 0, 3, 2, 0, 0, 0, 2], [0, 0, 0, 149, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 142, 0, 0, 0, 0, 8], [0, 0, 0, 1, 0, 0, 149, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 0, 127, 21, 0, 0], [0, 0, 1, 0, 0, 0, 0, 25, 123, 1, 0], [4, 0, 1, 0, 2, 0, 0, 0, 0, 143, 0], [0, 103, 0, 0, 0, 5, 0, 0, 0, 0, 42]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 87.87878787878788, "macro_f1": 87.0513366939069, "kappa": 0.8666666666666667}, "sample_count": 1650}, "SNR_4dB": {"snr": 4.0, "confusion_matrix": [[144, 0, 2, 0, 1, 0, 0, 0, 1, 2, 0], [0, 148, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 2, 133, 3, 0, 6, 2, 0, 1, 1, 1], [0, 0, 1, 147, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 150, 0, 0, 0, 0], [1, 0, 1, 0, 0, 1, 0, 129, 18, 0, 0], [0, 0, 2, 0, 0, 0, 0, 25, 123, 0, 0], [0, 0, 1, 0, 1, 0, 0, 0, 0, 148, 0], [0, 93, 0, 0, 0, 4, 0, 0, 0, 0, 53]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 89.39393939393939, "macro_f1": 88.67851303701329, "kappa": 0.8833333333333333}, "sample_count": 1650}, "SNR_6dB": {"snr": 6.0, "confusion_matrix": [[144, 0, 2, 0, 0, 0, 0, 0, 1, 3, 0], [0, 149, 0, 0, 0, 0, 0, 0, 0, 0, 1], [0, 3, 141, 3, 0, 1, 0, 0, 0, 0, 2], [0, 0, 1, 149, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 149, 0, 0, 0, 0, 1], [1, 0, 2, 1, 0, 0, 146, 0, 0, 0, 0], [3, 0, 2, 0, 0, 0, 0, 129, 16, 0, 0], [0, 0, 2, 0, 0, 0, 0, 15, 133, 0, 0], [1, 0, 3, 0, 0, 0, 0, 0, 0, 146, 0], [0, 88, 0, 0, 0, 0, 0, 0, 0, 0, 62]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 90.78787878787878, "macro_f1": 90.32913541197165, "kappa": 0.8986666666666667}, "sample_count": 1650}, "SNR_8dB": {"snr": 8.0, "confusion_matrix": [[145, 0, 1, 0, 0, 0, 1, 1, 0, 2, 0], [0, 149, 0, 0, 0, 0, 0, 0, 0, 0, 1], [2, 1, 139, 0, 0, 1, 3, 1, 0, 0, 3], [0, 0, 1, 148, 1, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 0, 1, 0, 0, 149, 0, 0, 0, 0], [1, 0, 2, 0, 0, 0, 0, 135, 12, 0, 0], [1, 0, 1, 0, 0, 0, 0, 13, 135, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 149, 0], [0, 94, 0, 0, 0, 3, 0, 0, 0, 0, 53]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 91.03030303030303, "macro_f1": 90.33458092806256, "kappa": 0.9013333333333333}, "sample_count": 1650}, "SNR_10dB": {"snr": 10.0, "confusion_matrix": [[148, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 139, 0, 0, 5, 1, 0, 1, 0, 0], [0, 0, 0, 148, 0, 0, 2, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 149, 0, 0, 0, 0, 1], [0, 0, 0, 0, 0, 0, 150, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 135, 14, 0, 0], [0, 1, 2, 0, 0, 0, 0, 20, 127, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 149, 0], [0, 82, 0, 0, 0, 2, 0, 0, 0, 0, 66]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 91.57575757575758, "macro_f1": 91.16315072609554, "kappa": 0.9073333333333333}, "sample_count": 1650}, "SNR_12dB": {"snr": 12.0, "confusion_matrix": [[145, 0, 0, 0, 0, 0, 0, 3, 0, 2, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 4, 139, 0, 0, 1, 2, 1, 0, 0, 2], [0, 0, 1, 148, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 149, 0, 0, 0, 0, 1], [0, 0, 1, 0, 0, 0, 147, 1, 1, 0, 0], [3, 0, 0, 0, 0, 0, 0, 133, 14, 0, 0], [1, 0, 1, 0, 0, 1, 0, 22, 124, 1, 0], [0, 0, 0, 0, 0, 0, 0, 1, 0, 149, 0], [0, 81, 0, 0, 0, 3, 0, 0, 0, 0, 66]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 90.9090909090909, "macro_f1": 90.49477850862536, "kappa": 0.9}, "sample_count": 1650}, "SNR_14dB": {"snr": 14.0, "confusion_matrix": [[146, 0, 1, 0, 0, 0, 0, 0, 1, 2, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 141, 0, 2, 3, 0, 1, 0, 0, 0], [0, 0, 3, 147, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 149, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 149, 0, 0, 0, 0, 1], [0, 0, 0, 0, 0, 0, 150, 0, 0, 0, 0], [2, 0, 1, 0, 0, 0, 0, 125, 22, 0, 0], [0, 0, 0, 0, 0, 0, 1, 21, 128, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 149, 0], [0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 56]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 90.30303030303031, "macro_f1": 89.71282939492903, "kappa": 0.8933333333333333}, "sample_count": 1650}, "SNR_16dB": {"snr": 16.0, "confusion_matrix": [[147, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 140, 1, 2, 2, 0, 0, 0, 1, 2], [0, 0, 3, 143, 0, 0, 3, 0, 0, 1, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 149, 0, 0, 0, 0], [2, 0, 2, 0, 0, 0, 1, 127, 18, 0, 0], [1, 0, 1, 0, 0, 0, 0, 25, 123, 0, 0], [0, 0, 2, 0, 0, 0, 0, 1, 1, 146, 0], [0, 87, 0, 0, 0, 2, 0, 0, 0, 0, 61]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 90.06060606060606, "macro_f1": 89.5278982162465, "kappa": 0.8906666666666667}, "sample_count": 1650}, "SNR_18dB": {"snr": 18.0, "confusion_matrix": [[148, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 140, 1, 1, 3, 1, 0, 0, 0, 1], [0, 0, 2, 147, 0, 0, 0, 1, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 1, 148, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 141, 8, 0, 0], [0, 0, 1, 0, 0, 0, 0, 16, 133, 0, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 148, 0], [0, 97, 0, 0, 0, 3, 0, 0, 0, 0, 50]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 91.21212121212122, "macro_f1": 90.45777261948163, "kappa": 0.9033333333333333}, "sample_count": 1650}}}