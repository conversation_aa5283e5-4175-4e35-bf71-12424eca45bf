{"metadata": {"class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "dataset_type": "rml", "total_samples": 33000, "snr_list": [-20.0, -18.0, -16.0, -14.0, -12.0, -10.0, -8.0, -6.0, -4.0, -2.0, 0.0, 2.0, 4.0, 6.0, 8.0, 10.0, 12.0, 14.0, 16.0, 18.0], "num_classes": 11}, "confusion_matrices": {"SNR_-20dB": {"snr": -20.0, "confusion_matrix": [[1, 4, 128, 3, 4, 3, 3, 0, 2, 1, 1], [0, 6, 127, 0, 4, 4, 4, 0, 0, 4, 1], [1, 3, 136, 0, 0, 4, 1, 0, 2, 1, 2], [3, 3, 131, 1, 3, 2, 1, 0, 0, 4, 2], [1, 5, 134, 1, 1, 5, 2, 1, 0, 0, 0], [3, 1, 137, 1, 0, 4, 3, 0, 0, 1, 0], [3, 5, 135, 1, 2, 3, 0, 0, 1, 0, 0], [1, 4, 136, 4, 2, 2, 0, 0, 0, 0, 1], [5, 9, 122, 4, 2, 2, 3, 0, 1, 1, 1], [0, 2, 140, 1, 2, 1, 0, 0, 0, 2, 2], [1, 6, 122, 4, 5, 6, 2, 2, 0, 1, 1]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.272727272727273, "macro_f1": 3.257658849096503, "kappa": 0.0019999999999998908}, "sample_count": 1650}, "SNR_-18dB": {"snr": -18.0, "confusion_matrix": [[3, 8, 131, 1, 1, 1, 1, 1, 2, 0, 1], [1, 7, 135, 1, 1, 1, 0, 0, 0, 2, 2], [1, 3, 137, 2, 3, 2, 0, 1, 0, 1, 0], [0, 5, 125, 3, 4, 7, 0, 1, 0, 3, 2], [1, 6, 132, 1, 0, 4, 2, 0, 0, 3, 1], [2, 4, 126, 1, 3, 3, 4, 0, 0, 6, 1], [2, 5, 132, 2, 2, 3, 1, 0, 1, 2, 0], [1, 4, 130, 3, 1, 4, 2, 1, 1, 1, 2], [4, 9, 105, 10, 9, 4, 4, 1, 1, 3, 0], [1, 4, 136, 1, 2, 4, 0, 0, 1, 1, 0], [1, 4, 133, 0, 3, 3, 3, 1, 0, 0, 2]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.636363636363637, "macro_f1": 3.798613853811042, "kappa": 0.006000000000000005}, "sample_count": 1650}, "SNR_-16dB": {"snr": -16.0, "confusion_matrix": [[1, 1, 135, 2, 2, 5, 1, 1, 0, 1, 1], [1, 12, 120, 4, 3, 1, 2, 0, 0, 5, 2], [2, 1, 129, 1, 3, 4, 2, 2, 3, 3, 0], [6, 3, 129, 1, 4, 2, 1, 0, 0, 3, 1], [4, 2, 130, 0, 1, 4, 3, 3, 2, 1, 0], [3, 7, 131, 2, 1, 2, 1, 0, 0, 3, 0], [3, 7, 128, 1, 2, 3, 3, 0, 0, 2, 1], [0, 2, 125, 5, 3, 6, 2, 2, 0, 3, 2], [16, 5, 59, 16, 12, 10, 7, 5, 8, 12, 0], [1, 1, 145, 0, 0, 1, 1, 0, 0, 1, 0], [3, 11, 129, 1, 0, 1, 1, 0, 0, 1, 3]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.878787878787879, "macro_f1": 4.989407152127631, "kappa": 0.008666666666666822}, "sample_count": 1650}, "SNR_-14dB": {"snr": -14.0, "confusion_matrix": [[2, 4, 126, 5, 2, 4, 3, 1, 0, 2, 1], [0, 13, 123, 2, 1, 2, 3, 0, 0, 1, 5], [3, 4, 130, 2, 2, 1, 5, 0, 0, 2, 1], [1, 2, 133, 1, 2, 4, 1, 0, 1, 4, 1], [2, 4, 127, 2, 3, 8, 3, 0, 1, 0, 0], [1, 6, 126, 4, 6, 4, 1, 2, 0, 0, 0], [4, 5, 105, 5, 5, 10, 3, 2, 0, 9, 2], [7, 5, 94, 9, 9, 6, 3, 0, 3, 13, 1], [22, 1, 7, 14, 11, 4, 9, 23, 38, 20, 1], [1, 3, 137, 0, 0, 0, 1, 0, 1, 5, 2], [1, 17, 117, 2, 2, 1, 2, 0, 0, 4, 4]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 12.303030303030303, "macro_f1": 8.48484944582609, "kappa": 0.03533333333333333}, "sample_count": 1650}, "SNR_-12dB": {"snr": -12.0, "confusion_matrix": [[2, 5, 130, 1, 4, 4, 2, 1, 0, 1, 0], [3, 50, 78, 0, 1, 4, 2, 0, 0, 3, 9], [3, 8, 126, 1, 3, 4, 2, 2, 1, 0, 0], [6, 5, 121, 4, 2, 3, 1, 3, 1, 2, 2], [2, 5, 128, 4, 3, 3, 2, 2, 1, 0, 0], [2, 8, 120, 1, 2, 9, 0, 1, 1, 3, 3], [6, 3, 80, 9, 7, 8, 18, 6, 3, 8, 2], [12, 3, 33, 12, 16, 12, 14, 16, 19, 11, 2], [14, 0, 1, 0, 6, 2, 3, 31, 66, 26, 1], [7, 2, 128, 6, 3, 2, 0, 0, 0, 1, 1], [0, 37, 89, 0, 4, 5, 3, 0, 0, 4, 8]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 18.363636363636363, "macro_f1": 15.753263684744987, "kappa": 0.10199999999999998}, "sample_count": 1650}, "SNR_-10dB": {"snr": -10.0, "confusion_matrix": [[5, 4, 106, 4, 8, 9, 2, 3, 0, 7, 2], [3, 87, 42, 0, 1, 4, 1, 0, 0, 0, 12], [4, 4, 132, 3, 1, 4, 1, 0, 0, 1, 0], [5, 5, 115, 10, 3, 3, 4, 0, 1, 4, 0], [8, 4, 108, 3, 8, 6, 3, 4, 1, 1, 4], [2, 19, 95, 1, 7, 18, 1, 0, 1, 1, 5], [6, 2, 16, 27, 2, 5, 65, 4, 12, 9, 2], [18, 2, 3, 10, 9, 5, 3, 23, 52, 25, 0], [13, 0, 1, 0, 0, 0, 0, 43, 86, 7, 0], [4, 7, 100, 10, 10, 3, 2, 4, 4, 4, 2], [3, 63, 54, 1, 2, 15, 3, 0, 0, 1, 8]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 27.03030303030303, "macro_f1": 23.666183635360127, "kappa": 0.19733333333333347}, "sample_count": 1650}, "SNR_-8dB": {"snr": -8.0, "confusion_matrix": [[12, 7, 66, 14, 9, 13, 5, 5, 4, 14, 1], [0, 107, 3, 2, 1, 13, 1, 2, 2, 0, 19], [3, 4, 128, 2, 3, 2, 2, 0, 0, 4, 2], [8, 2, 65, 20, 6, 7, 18, 5, 4, 13, 2], [8, 8, 51, 11, 20, 19, 9, 9, 5, 9, 1], [3, 16, 27, 1, 11, 72, 4, 2, 2, 3, 9], [4, 1, 1, 27, 1, 0, 105, 0, 8, 3, 0], [17, 0, 1, 0, 1, 0, 1, 49, 63, 18, 0], [6, 0, 2, 0, 0, 0, 1, 60, 80, 1, 0], [14, 4, 57, 13, 4, 12, 9, 9, 12, 13, 3], [0, 117, 3, 1, 1, 9, 1, 1, 0, 2, 15]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 37.63636363636363, "macro_f1": 33.59583338835443, "kappa": 0.31400000000000006}, "sample_count": 1650}, "SNR_-6dB": {"snr": -6.0, "confusion_matrix": [[23, 2, 10, 18, 18, 7, 6, 13, 20, 31, 2], [0, 123, 0, 0, 0, 3, 0, 0, 0, 0, 24], [0, 1, 141, 4, 2, 0, 0, 0, 0, 0, 2], [6, 2, 15, 45, 2, 2, 53, 4, 7, 13, 1], [13, 0, 4, 3, 62, 19, 8, 11, 15, 13, 2], [2, 8, 0, 1, 14, 103, 1, 2, 1, 2, 16], [0, 0, 3, 9, 0, 0, 134, 2, 2, 0, 0], [9, 0, 4, 0, 0, 0, 0, 71, 63, 3, 0], [2, 0, 1, 0, 0, 0, 1, 33, 113, 0, 0], [24, 1, 7, 8, 16, 4, 7, 22, 31, 28, 2], [0, 103, 0, 0, 0, 12, 0, 0, 0, 0, 35]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 53.21212121212121, "macro_f1": 50.07363297770008, "kappa": 0.4853333333333333}, "sample_count": 1650}, "SNR_-4dB": {"snr": -4.0, "confusion_matrix": [[47, 1, 3, 5, 7, 0, 0, 17, 22, 48, 0], [0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 11], [1, 3, 131, 1, 5, 2, 3, 2, 0, 0, 2], [1, 0, 2, 119, 0, 0, 23, 0, 0, 5, 0], [8, 0, 0, 0, 120, 5, 0, 3, 7, 5, 2], [0, 3, 0, 0, 0, 132, 0, 0, 0, 1, 14], [0, 0, 2, 1, 0, 0, 147, 0, 0, 0, 0], [6, 0, 0, 0, 0, 0, 0, 91, 51, 1, 1], [2, 0, 1, 0, 0, 0, 0, 22, 125, 0, 0], [39, 0, 2, 3, 4, 1, 2, 13, 18, 68, 0], [0, 103, 0, 1, 0, 11, 0, 0, 0, 0, 35]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 69.93939393939394, "macro_f1": 68.48896766801184, "kappa": 0.6693333333333333}, "sample_count": 1650}, "SNR_-2dB": {"snr": -2.0, "confusion_matrix": [[102, 0, 1, 0, 2, 0, 0, 13, 13, 19, 0], [0, 146, 0, 0, 0, 0, 0, 0, 0, 0, 4], [1, 2, 139, 2, 0, 1, 0, 0, 1, 4, 0], [0, 0, 4, 135, 0, 0, 8, 1, 2, 0, 0], [3, 0, 0, 0, 144, 2, 0, 1, 0, 0, 0], [0, 1, 0, 0, 1, 142, 0, 0, 0, 0, 6], [0, 0, 0, 0, 0, 0, 149, 0, 0, 1, 0], [1, 0, 2, 0, 0, 0, 0, 134, 13, 0, 0], [0, 0, 1, 0, 0, 0, 0, 18, 130, 1, 0], [18, 0, 2, 0, 2, 0, 0, 9, 9, 110, 0], [0, 114, 0, 0, 0, 12, 0, 0, 0, 0, 24]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 82.12121212121211, "macro_f1": 80.4622175208389, "kappa": 0.8033333333333333}, "sample_count": 1650}, "SNR_0dB": {"snr": 0.0, "confusion_matrix": [[142, 0, 0, 0, 0, 0, 0, 3, 1, 4, 0], [0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 3], [1, 4, 142, 0, 0, 3, 0, 0, 0, 0, 0], [0, 0, 4, 145, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 149, 0, 0, 0, 0], [1, 0, 1, 0, 0, 0, 0, 136, 12, 0, 0], [0, 0, 1, 0, 0, 0, 0, 14, 135, 0, 0], [4, 0, 0, 0, 0, 0, 0, 0, 0, 146, 0], [0, 102, 0, 0, 0, 2, 0, 0, 0, 0, 46]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 90.18181818181819, "macro_f1": 89.37951920774209, "kappa": 0.892}, "sample_count": 1650}, "SNR_2dB": {"snr": 2.0, "confusion_matrix": [[144, 0, 2, 0, 0, 0, 0, 0, 1, 3, 0], [0, 141, 0, 0, 0, 0, 0, 0, 0, 0, 9], [2, 4, 133, 1, 3, 1, 2, 0, 0, 4, 0], [0, 0, 0, 150, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 1, 1, 0, 0, 148, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 0, 144, 3, 1, 0], [0, 0, 1, 0, 0, 0, 0, 5, 144, 0, 0], [0, 1, 1, 0, 1, 0, 0, 0, 0, 147, 0], [0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 55]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 91.27272727272727, "macro_f1": 90.75102493803253, "kappa": 0.904}, "sample_count": 1650}, "SNR_4dB": {"snr": 4.0, "confusion_matrix": [[146, 0, 2, 0, 0, 0, 0, 0, 0, 2, 0], [0, 144, 0, 0, 0, 0, 0, 0, 0, 0, 6], [2, 7, 129, 1, 0, 6, 3, 0, 1, 1, 0], [0, 0, 3, 147, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 145, 0, 1, 1, 0], [0, 0, 1, 0, 0, 0, 0, 147, 2, 0, 0], [0, 0, 2, 0, 0, 0, 0, 3, 144, 1, 0], [0, 0, 2, 0, 0, 0, 0, 0, 0, 148, 0], [0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 54]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 91.15151515151516, "macro_f1": 90.60562072049791, "kappa": 0.9026666666666667}, "sample_count": 1650}, "SNR_6dB": {"snr": 6.0, "confusion_matrix": [[149, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], [0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 3], [1, 4, 137, 1, 2, 0, 4, 0, 0, 1, 0], [0, 0, 1, 149, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 150, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 148, 1, 1, 0], [0, 0, 0, 0, 0, 0, 0, 8, 141, 1, 0], [0, 0, 2, 0, 1, 0, 0, 0, 0, 147, 0], [0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 55]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.3030303030303, "macro_f1": 91.72389609157393, "kappa": 0.9153333333333333}, "sample_count": 1650}, "SNR_8dB": {"snr": 8.0, "confusion_matrix": [[148, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0], [0, 149, 0, 0, 0, 0, 0, 0, 0, 0, 1], [4, 5, 133, 3, 1, 1, 1, 1, 0, 1, 0], [0, 0, 0, 150, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 148, 0, 0, 0, 0], [1, 0, 1, 0, 0, 0, 0, 146, 2, 0, 0], [1, 0, 1, 0, 0, 0, 0, 10, 138, 0, 0], [0, 1, 3, 0, 0, 0, 0, 0, 0, 146, 0], [0, 93, 0, 0, 0, 1, 0, 0, 0, 0, 56]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 91.75757575757576, "macro_f1": 91.18765396973711, "kappa": 0.9093333333333333}, "sample_count": 1650}, "SNR_10dB": {"snr": 10.0, "confusion_matrix": [[149, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 5, 134, 1, 3, 1, 2, 0, 0, 1, 1], [0, 0, 1, 149, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 147, 0, 0, 0, 0], [2, 0, 2, 0, 0, 0, 0, 144, 2, 0, 0], [0, 0, 1, 0, 0, 0, 0, 10, 139, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 149, 0], [0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 59]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.12121212121212, "macro_f1": 91.60997139510309, "kappa": 0.9133333333333333}, "sample_count": 1650}, "SNR_12dB": {"snr": 12.0, "confusion_matrix": [[148, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 9, 128, 1, 3, 3, 2, 1, 1, 0, 2], [0, 0, 1, 149, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 149, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 150, 0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 5, 144, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 1, 149, 0], [0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 67]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.96969696969697, "macro_f1": 92.62257882575692, "kappa": 0.9226666666666666}, "sample_count": 1650}, "SNR_14dB": {"snr": 14.0, "confusion_matrix": [[148, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 6, 133, 2, 2, 1, 2, 0, 0, 1, 2], [0, 0, 2, 147, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 149, 0, 0, 1, 0], [2, 0, 1, 0, 0, 0, 0, 146, 1, 0, 0], [0, 0, 1, 0, 0, 0, 0, 14, 135, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 149, 0], [0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 72]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.66666666666666, "macro_f1": 92.37136054702596, "kappa": 0.9193333333333333}, "sample_count": 1650}, "SNR_16dB": {"snr": 16.0, "confusion_matrix": [[146, 0, 1, 1, 0, 0, 0, 0, 0, 2, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 10, 127, 3, 3, 2, 2, 0, 0, 3, 0], [0, 1, 2, 147, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 0, 1, 0, 0, 149, 0, 0, 0, 0], [3, 0, 0, 0, 0, 0, 0, 143, 4, 0, 0], [0, 0, 0, 1, 0, 0, 0, 8, 140, 1, 0], [0, 0, 2, 0, 0, 0, 0, 0, 1, 147, 0], [0, 88, 0, 0, 0, 0, 0, 0, 0, 0, 62]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 91.57575757575758, "macro_f1": 91.15914217198063, "kappa": 0.9073333333333333}, "sample_count": 1650}, "SNR_18dB": {"snr": 18.0, "confusion_matrix": [[148, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], [0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 2, 138, 3, 0, 1, 1, 0, 0, 4, 1], [0, 0, 1, 148, 0, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 149, 0, 0, 1, 0], [0, 1, 0, 0, 0, 0, 0, 148, 1, 0, 0], [0, 0, 3, 0, 0, 0, 0, 10, 137, 0, 0], [0, 0, 3, 0, 0, 0, 0, 0, 0, 147, 0], [0, 84, 0, 0, 0, 1, 0, 0, 0, 0, 65]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.72727272727272, "macro_f1": 92.29592373281574, "kappa": 0.9199999999999999}, "sample_count": 1650}}}