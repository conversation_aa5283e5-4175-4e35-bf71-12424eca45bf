{"metadata": {"class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "dataset_type": "rml", "total_samples": 66000, "snr_list": [-20.0, -18.0, -16.0, -14.0, -12.0, -10.0, -8.0, -6.0, -4.0, -2.0, 0.0, 2.0, 4.0, 6.0, 8.0, 10.0, 12.0, 14.0, 16.0, 18.0], "num_classes": 11}, "confusion_matrices": {"SNR_-20dB": {"snr": -20.0, "confusion_matrix": [[1, 7, 272, 1, 5, 10, 0, 0, 0, 4, 0], [1, 14, 263, 0, 5, 7, 1, 1, 0, 3, 5], [0, 8, 274, 0, 5, 7, 1, 0, 0, 3, 2], [0, 9, 261, 1, 8, 8, 1, 0, 1, 8, 3], [1, 11, 264, 0, 8, 9, 1, 2, 0, 2, 2], [3, 8, 271, 1, 5, 6, 1, 0, 0, 1, 4], [0, 4, 274, 1, 9, 7, 0, 0, 0, 3, 2], [1, 11, 257, 0, 11, 11, 1, 1, 0, 4, 3], [2, 9, 254, 3, 12, 8, 1, 3, 0, 7, 1], [2, 6, 266, 1, 10, 8, 2, 0, 0, 2, 3], [3, 24, 253, 1, 6, 6, 3, 1, 0, 2, 1]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.333333333333334, "macro_f1": 3.1712768544103875, "kappa": 0.002666666666666706}, "sample_count": 3300}, "SNR_-18dB": {"snr": -18.0, "confusion_matrix": [[0, 7, 276, 0, 2, 11, 1, 1, 1, 0, 1], [0, 16, 260, 1, 5, 9, 0, 0, 1, 4, 4], [0, 5, 270, 0, 7, 5, 2, 0, 0, 6, 5], [1, 5, 270, 0, 13, 5, 0, 1, 0, 1, 4], [2, 8, 265, 1, 8, 11, 3, 0, 0, 0, 2], [1, 11, 261, 1, 8, 8, 2, 0, 1, 4, 3], [3, 7, 260, 0, 7, 13, 0, 3, 0, 4, 3], [2, 7, 269, 0, 3, 13, 3, 0, 1, 2, 0], [3, 9, 227, 3, 20, 16, 4, 1, 4, 9, 4], [1, 7, 277, 1, 7, 5, 0, 0, 0, 1, 1], [2, 15, 261, 0, 4, 11, 1, 0, 0, 1, 5]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.454545454545455, "macro_f1": 3.5696640524809102, "kappa": 0.0040000000000000036}, "sample_count": 3300}, "SNR_-16dB": {"snr": -16.0, "confusion_matrix": [[1, 4, 282, 1, 3, 4, 1, 0, 0, 4, 0], [0, 22, 255, 1, 5, 9, 2, 0, 2, 1, 3], [2, 10, 270, 2, 6, 4, 2, 1, 0, 1, 2], [2, 5, 268, 2, 7, 7, 2, 1, 0, 5, 1], [1, 5, 271, 0, 7, 13, 1, 1, 0, 0, 1], [2, 11, 256, 0, 16, 11, 0, 0, 1, 1, 2], [0, 7, 263, 1, 13, 9, 1, 1, 0, 3, 2], [2, 3, 261, 0, 6, 12, 5, 3, 0, 6, 2], [13, 4, 163, 10, 37, 12, 10, 9, 7, 32, 3], [1, 8, 268, 0, 7, 10, 2, 0, 0, 3, 1], [0, 22, 255, 0, 5, 5, 2, 0, 0, 6, 5]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 10.06060606060606, "macro_f1": 4.63137718298243, "kappa": 0.010666666666666713}, "sample_count": 3300}, "SNR_-14dB": {"snr": -14.0, "confusion_matrix": [[1, 10, 265, 2, 7, 7, 1, 2, 0, 2, 3], [1, 39, 229, 1, 7, 6, 1, 1, 1, 3, 11], [1, 7, 279, 0, 8, 3, 0, 0, 1, 0, 1], [1, 8, 269, 2, 4, 10, 2, 1, 0, 2, 1], [3, 9, 267, 0, 8, 8, 1, 0, 0, 3, 1], [2, 11, 254, 1, 9, 12, 0, 2, 0, 2, 7], [0, 15, 244, 3, 14, 14, 4, 0, 0, 5, 1], [10, 3, 197, 4, 34, 20, 4, 5, 4, 17, 2], [46, 1, 26, 9, 33, 19, 9, 60, 22, 67, 8], [1, 5, 267, 0, 9, 13, 0, 0, 0, 4, 1], [3, 42, 229, 0, 2, 11, 1, 2, 0, 3, 7]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 11.606060606060606, "macro_f1": 6.6208649035724365, "kappa": 0.027666666666666395}, "sample_count": 3300}, "SNR_-12dB": {"snr": -12.0, "confusion_matrix": [[3, 11, 259, 1, 6, 10, 1, 1, 0, 4, 4], [2, 69, 190, 0, 3, 12, 3, 0, 0, 2, 19], [1, 9, 274, 0, 5, 6, 1, 1, 0, 1, 2], [0, 14, 252, 3, 9, 11, 2, 0, 1, 6, 2], [0, 9, 261, 1, 10, 10, 1, 0, 1, 4, 3], [3, 10, 253, 2, 5, 18, 1, 0, 1, 1, 6], [9, 10, 170, 10, 26, 19, 32, 4, 1, 14, 5], [31, 5, 84, 5, 50, 23, 11, 34, 16, 36, 5], [59, 0, 6, 0, 7, 0, 3, 120, 54, 51, 0], [1, 4, 267, 6, 4, 10, 1, 0, 0, 3, 4], [2, 77, 173, 1, 11, 12, 0, 0, 1, 3, 20]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 15.757575757575756, "macro_f1": 12.605809053444576, "kappa": 0.07333333333333336}, "sample_count": 3300}, "SNR_-10dB": {"snr": -10.0, "confusion_matrix": [[4, 9, 240, 4, 15, 13, 1, 2, 1, 7, 4], [3, 136, 88, 1, 5, 25, 0, 0, 0, 1, 41], [1, 16, 270, 0, 2, 9, 1, 0, 0, 0, 1], [3, 7, 239, 2, 16, 15, 7, 3, 1, 5, 2], [2, 8, 229, 0, 21, 24, 2, 1, 1, 9, 3], [1, 23, 182, 1, 18, 57, 3, 0, 1, 3, 11], [6, 5, 47, 58, 18, 14, 114, 21, 4, 11, 2], [56, 1, 15, 8, 18, 15, 7, 76, 31, 71, 2], [37, 0, 3, 0, 0, 0, 0, 153, 95, 11, 1], [3, 18, 230, 0, 18, 17, 1, 4, 0, 7, 2], [3, 109, 100, 1, 5, 34, 0, 0, 0, 5, 43]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 25.0, "macro_f1": 22.96192830237059, "kappa": 0.17500000000000004}, "sample_count": 3300}, "SNR_-8dB": {"snr": -8.0, "confusion_matrix": [[23, 10, 162, 5, 35, 27, 3, 8, 2, 23, 2], [0, 185, 8, 0, 2, 28, 0, 0, 1, 0, 76], [0, 6, 269, 0, 8, 10, 0, 0, 0, 6, 1], [6, 2, 150, 22, 33, 16, 36, 3, 4, 23, 5], [11, 10, 127, 7, 69, 36, 4, 10, 4, 15, 7], [3, 19, 68, 1, 29, 151, 2, 0, 1, 8, 18], [1, 2, 5, 62, 0, 1, 215, 7, 2, 4, 1], [61, 0, 4, 1, 4, 0, 2, 138, 49, 40, 1], [7, 0, 1, 0, 0, 0, 0, 128, 163, 1, 0], [20, 6, 157, 7, 30, 25, 4, 15, 3, 30, 3], [2, 153, 10, 1, 5, 32, 1, 0, 0, 0, 96]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 41.24242424242424, "macro_f1": 38.7540991541238, "kappa": 0.35366666666666646}, "sample_count": 3300}, "SNR_-6dB": {"snr": -6.0, "confusion_matrix": [[53, 3, 35, 8, 55, 20, 9, 38, 12, 64, 3], [0, 208, 0, 1, 0, 8, 0, 0, 0, 0, 83], [0, 5, 272, 0, 9, 5, 1, 0, 0, 4, 4], [16, 2, 35, 120, 12, 12, 79, 4, 3, 17, 0], [10, 1, 21, 2, 175, 34, 4, 9, 10, 29, 5], [5, 17, 5, 2, 18, 219, 0, 1, 4, 2, 27], [1, 0, 3, 21, 0, 0, 274, 1, 0, 0, 0], [25, 0, 3, 0, 1, 1, 1, 195, 72, 2, 0], [2, 0, 3, 0, 0, 0, 0, 52, 243, 0, 0], [57, 4, 27, 9, 38, 17, 6, 39, 20, 79, 4], [1, 153, 0, 0, 2, 28, 0, 0, 1, 0, 115]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 59.18181818181818, "macro_f1": 56.98235634047456, "kappa": 0.551}, "sample_count": 3300}, "SNR_-4dB": {"snr": -4.0, "confusion_matrix": [[119, 0, 2, 1, 24, 3, 1, 46, 8, 96, 0], [0, 240, 0, 0, 0, 0, 0, 0, 0, 0, 60], [1, 6, 273, 1, 6, 5, 1, 1, 0, 4, 2], [0, 0, 4, 249, 1, 0, 42, 3, 1, 0, 0], [9, 0, 1, 0, 263, 8, 0, 4, 4, 11, 0], [1, 2, 0, 0, 11, 257, 0, 0, 1, 2, 26], [0, 1, 3, 6, 0, 0, 289, 0, 1, 0, 0], [6, 0, 2, 0, 0, 0, 0, 199, 92, 1, 0], [2, 0, 5, 0, 0, 0, 0, 31, 262, 0, 0], [96, 0, 4, 2, 9, 0, 1, 45, 20, 123, 0], [0, 167, 0, 0, 0, 18, 0, 0, 0, 0, 115]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 72.3939393939394, "macro_f1": 71.57131040200392, "kappa": 0.6963333333333334}, "sample_count": 3300}, "SNR_-2dB": {"snr": -2.0, "confusion_matrix": [[204, 0, 3, 1, 8, 0, 0, 26, 4, 54, 0], [0, 253, 0, 0, 0, 0, 0, 0, 0, 0, 47], [0, 9, 275, 1, 3, 8, 0, 1, 0, 0, 3], [0, 0, 3, 280, 0, 0, 17, 0, 0, 0, 0], [2, 0, 0, 0, 298, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 1, 283, 0, 0, 0, 0, 15], [0, 0, 5, 0, 0, 0, 295, 0, 0, 0, 0], [2, 0, 4, 0, 0, 0, 0, 230, 64, 0, 0], [0, 0, 3, 0, 0, 0, 0, 26, 270, 1, 0], [87, 0, 4, 2, 5, 0, 1, 14, 4, 183, 0], [0, 177, 0, 0, 0, 12, 0, 0, 0, 0, 111]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 81.27272727272728, "macro_f1": 80.7111395802065, "kappa": 0.7939999999999999}, "sample_count": 3300}, "SNR_0dB": {"snr": 0.0, "confusion_matrix": [[273, 0, 1, 0, 1, 0, 0, 8, 4, 13, 0], [0, 270, 0, 0, 0, 3, 0, 0, 0, 0, 27], [2, 11, 272, 0, 3, 7, 3, 0, 0, 1, 1], [0, 0, 3, 296, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 296, 0, 0, 0, 0, 2], [0, 0, 4, 0, 0, 0, 296, 0, 0, 0, 0], [0, 0, 4, 0, 0, 0, 0, 254, 42, 0, 0], [0, 0, 3, 0, 0, 0, 0, 17, 279, 1, 0], [17, 0, 1, 0, 0, 0, 0, 1, 2, 279, 0], [0, 165, 0, 0, 0, 15, 0, 0, 0, 0, 120]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 88.93939393939394, "macro_f1": 88.49607147041598, "kappa": 0.8783333333333333}, "sample_count": 3300}, "SNR_2dB": {"snr": 2.0, "confusion_matrix": [[290, 0, 4, 0, 0, 0, 0, 3, 0, 3, 0], [0, 275, 0, 0, 0, 1, 0, 0, 0, 0, 24], [1, 8, 275, 1, 3, 5, 0, 2, 0, 2, 3], [0, 0, 0, 300, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 1], [0, 1, 1, 0, 0, 0, 298, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 266, 31, 0, 0], [0, 0, 1, 0, 0, 0, 0, 17, 281, 1, 0], [7, 0, 2, 0, 1, 0, 0, 0, 0, 290, 0], [0, 176, 0, 0, 0, 2, 0, 0, 0, 0, 122]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 90.78787878787878, "macro_f1": 90.40119692890741, "kappa": 0.8986666666666667}, "sample_count": 3300}, "SNR_4dB": {"snr": 4.0, "confusion_matrix": [[294, 0, 4, 0, 0, 0, 0, 0, 0, 2, 0], [0, 293, 0, 0, 0, 0, 0, 0, 0, 0, 7], [0, 8, 272, 1, 4, 11, 1, 0, 0, 2, 1], [0, 0, 2, 296, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 300, 0, 0, 0, 0], [1, 0, 3, 0, 0, 0, 0, 278, 18, 0, 0], [0, 0, 3, 0, 0, 0, 0, 10, 287, 0, 0], [0, 0, 2, 0, 1, 0, 0, 0, 0, 297, 0], [0, 169, 0, 0, 0, 0, 0, 0, 0, 0, 131]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.36363636363636, "macro_f1": 91.9775851543662, "kappa": 0.916}, "sample_count": 3300}, "SNR_6dB": {"snr": 6.0, "confusion_matrix": [[296, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0], [0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 6], [0, 13, 269, 1, 3, 8, 1, 1, 0, 3, 1], [0, 0, 4, 296, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 1], [0, 0, 5, 0, 0, 0, 295, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 0, 280, 17, 0, 0], [0, 0, 3, 0, 0, 0, 0, 15, 282, 0, 0], [0, 0, 3, 0, 0, 0, 0, 0, 2, 295, 0], [0, 164, 0, 0, 0, 1, 0, 0, 0, 0, 135]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.15151515151516, "macro_f1": 91.82297563168184, "kappa": 0.9136666666666666}, "sample_count": 3300}, "SNR_8dB": {"snr": 8.0, "confusion_matrix": [[291, 0, 5, 0, 0, 0, 1, 0, 0, 3, 0], [0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 1], [1, 6, 274, 1, 7, 1, 4, 0, 0, 3, 3], [0, 0, 2, 297, 0, 1, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 298, 0, 0, 0, 0], [1, 0, 2, 0, 0, 0, 0, 278, 19, 0, 0], [0, 0, 1, 0, 0, 0, 0, 5, 294, 0, 0], [0, 0, 4, 0, 0, 1, 0, 1, 0, 294, 0], [0, 175, 0, 0, 0, 0, 0, 0, 0, 0, 125]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.42424242424242, "macro_f1": 91.96498168735924, "kappa": 0.9166666666666666}, "sample_count": 3300}, "SNR_10dB": {"snr": 10.0, "confusion_matrix": [[299, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 6, 272, 2, 2, 10, 3, 1, 0, 1, 2], [0, 0, 2, 298, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 1, 1, 0, 0, 0, 298, 0, 0, 0, 0], [2, 0, 2, 0, 0, 0, 0, 284, 12, 0, 0], [2, 0, 3, 1, 0, 0, 0, 4, 290, 0, 0], [0, 0, 1, 1, 0, 0, 0, 0, 0, 298, 0], [0, 168, 0, 0, 0, 0, 0, 0, 0, 0, 132]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 93.06060606060606, "macro_f1": 92.65503466807282, "kappa": 0.9236666666666666}, "sample_count": 3300}, "SNR_12dB": {"snr": 12.0, "confusion_matrix": [[298, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 9, 270, 1, 1, 9, 2, 2, 0, 3, 3], [0, 0, 4, 296, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 0, 297, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 0, 282, 16, 0, 0], [2, 0, 6, 0, 0, 0, 0, 12, 279, 1, 0], [0, 0, 3, 0, 0, 0, 0, 0, 0, 297, 0], [0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 142]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.75757575757576, "macro_f1": 92.43823009697233, "kappa": 0.9203333333333333}, "sample_count": 3300}, "SNR_14dB": {"snr": 14.0, "confusion_matrix": [[294, 0, 4, 0, 0, 0, 0, 0, 0, 2, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 5, 282, 1, 2, 6, 0, 1, 0, 1, 0], [0, 0, 6, 294, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 300, 0, 0, 0, 0], [3, 0, 3, 0, 0, 0, 0, 277, 17, 0, 0], [0, 0, 1, 0, 0, 0, 1, 11, 287, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 299, 0], [0, 171, 0, 0, 0, 0, 0, 0, 0, 0, 129]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.78787878787878, "macro_f1": 92.35060149436755, "kappa": 0.9206666666666666}, "sample_count": 3300}, "SNR_16dB": {"snr": 16.0, "confusion_matrix": [[294, 0, 2, 0, 0, 0, 0, 0, 0, 4, 0], [0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 1], [1, 7, 277, 0, 5, 4, 4, 0, 0, 1, 1], [0, 0, 3, 295, 0, 1, 1, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 299, 0, 0, 0, 0], [4, 0, 2, 0, 0, 0, 2, 281, 11, 0, 0], [0, 1, 2, 1, 0, 0, 0, 9, 287, 0, 0], [0, 0, 2, 0, 0, 0, 1, 0, 2, 295, 0], [0, 163, 0, 0, 0, 0, 0, 0, 0, 0, 137]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.84848484848484, "macro_f1": 92.49183442928852, "kappa": 0.9213333333333333}, "sample_count": 3300}, "SNR_18dB": {"snr": 18.0, "confusion_matrix": [[297, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 6, 276, 0, 2, 8, 1, 2, 0, 2, 2], [0, 1, 2, 297, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 2, 0, 0, 0, 298, 0, 0, 0, 0], [2, 0, 0, 0, 0, 0, 1, 280, 17, 0, 0], [0, 0, 5, 0, 0, 0, 0, 11, 284, 0, 0], [0, 0, 3, 1, 1, 0, 0, 0, 0, 295, 0], [0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 124]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 92.45454545454545, "macro_f1": 91.99352089157131, "kappa": 0.917}, "sample_count": 3300}}}