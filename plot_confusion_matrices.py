import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import os

# ==================== 字体和样式参数设置 ====================
# 设置字体为Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'

# ==================== 信噪比选择参数 ====================
SNR_VALUES = [0, 10]          # 要绘制的SNR值列表，可以添加更多值如 [-10, 0, 10, 20]
GENERATE_COMBINED = True      # 是否生成结合版本（所有SNR在一张图中）
# ==================== 信噪比参数结束 ====================

# 字体大小设置
TITLE_FONT_SIZE = 18          # 模型名称标题字体大小
AXIS_LABEL_FONT_SIZE = 18     # 坐标轴标签字体大小 (Predict class, Actual class)
TICK_LABEL_FONT_SIZE = 18     # 刻度标签字体大小 (调制方式名称)
ANNOTATION_FONT_SIZE = 12     # 混淆矩阵内数值标注字体大小
COLORBAR_LABEL_FONT_SIZE = 18 # 颜色条标签字体大小
COLORBAR_TICK_FONT_SIZE = 14  # 颜色条刻度字体大小

# 颜色条参数设置
COLORBAR_WIDTH = 0.005        # 颜色条宽度
COLORBAR_LEFT = 0.93          # 颜色条左边距
COLORBAR_BOTTOM = 0.25        # 颜色条底部位置
COLORBAR_HEIGHT = 0.6        # 颜色条高度
COLORBAR_LABELPAD = 25        # 颜色条标签与颜色条的距离

# 组合图颜色条位置调整参数 - 可以独立调整每个颜色条
COMBINED_COLORBAR_LEFT = 0.93     # 组合图颜色条左边距
COMBINED_COLORBAR_WIDTH = 0.005   # 组合图颜色条宽度
COMBINED_COLORBAR_HEIGHT_RATIO = 0.6      # 组合图颜色条高度比例（相对于每行高度）

# 独立调整每个颜色条的垂直位置（根据SNR顺序）
# 第一个值对应第一个SNR，第二个值对应第二个SNR，以此类推
COLORBAR_VERTICAL_OFFSETS = [-0.02, 0]  # 每个颜色条的垂直偏移调整，可以独立设置

# 图形尺寸和布局参数
FIGURE_WIDTH = 25             # 图形宽度
FIGURE_HEIGHT = 5             # 图形高度
SUBPLOT_LEFT = 0.08           # 子图左边距
SUBPLOT_RIGHT = 0.92          # 子图右边距
SUBPLOT_BOTTOM = 0.15         # 子图底部边距
SUBPLOT_TOP = 0.95            # 子图顶部边距
SUBPLOT_WSPACE = 0.32          # 子图之间的水平间距
# ==================== 参数设置结束 ====================

def load_confusion_matrix_data(model_path):
    """加载混淆矩阵数据"""
    json_file = None
    for root, dirs, files in os.walk(model_path):
        for file in files:
            if file == 'snr_confusion_matrices.json':
                json_file = os.path.join(root, file)
                break
        if json_file:
            break
    
    if not json_file:
        print(f"未找到 {model_path} 的混淆矩阵数据文件")
        return None
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data

def normalize_confusion_matrix(cm):
    """将混淆矩阵归一化为百分比"""
    cm = np.array(cm)
    # 按行归一化（每行代表真实类别）
    row_sums = cm.sum(axis=1, keepdims=True)
    # 避免除零
    row_sums[row_sums == 0] = 1
    normalized_cm = cm / row_sums
    return normalized_cm

def plot_confusion_matrices_grid(snr_values, output_dir):
    """绘制混淆矩阵网格图"""
    # 模型名称和对应的文件夹（按指定顺序：CLDNN AWN MAMC MAWDN MWRNN）
    from collections import OrderedDict
    models = OrderedDict([
        ('CLDNN', '混淆矩阵/cldnn'),
        ('AWN', '混淆矩阵/awn'),
        ('MAMC', '混淆矩阵/mamc'),
        ('MAWDN', '混淆矩阵/mawdn'),
        ('MWRNN', '混淆矩阵/mwrnn')
    ])
    
    # 加载所有模型的数据
    model_data = {}
    class_names = None
    
    for model_name, model_path in models.items():
        data = load_confusion_matrix_data(model_path)
        if data is not None:
            model_data[model_name] = data
            if class_names is None:
                class_names = data['metadata']['class_names']
    
    if not model_data:
        print("未找到任何有效的混淆矩阵数据")
        return
    
    # 为每个SNR值创建一个图
    for snr in snr_values:
        # 创建更宽的图形，为颜色条留出空间
        fig, axes = plt.subplots(1, 5, figsize=(FIGURE_WIDTH, FIGURE_HEIGHT))

        # 调整子图之间的间距，取消总标题
        plt.subplots_adjust(left=SUBPLOT_LEFT, right=SUBPLOT_RIGHT, bottom=SUBPLOT_BOTTOM,
                          top=SUBPLOT_TOP, wspace=SUBPLOT_WSPACE)

        for idx, (model_name, data) in enumerate(model_data.items()):
            snr_key = f'SNR_{snr}dB'

            if snr_key in data['confusion_matrices']:
                cm = data['confusion_matrices'][snr_key]['confusion_matrix']
                normalized_cm = normalize_confusion_matrix(cm)

                # 绘制热图，确保是正方形
                im = axes[idx].imshow(normalized_cm, cmap='Blues', aspect='equal', vmin=0, vmax=1)

                # 设置标题
                axes[idx].set_title(model_name, fontsize=TITLE_FONT_SIZE, fontweight='bold', family='Times New Roman')

                # 设置坐标轴标签 - 每个图都显示
                axes[idx].set_xticks(range(len(class_names)))
                axes[idx].set_yticks(range(len(class_names)))
                axes[idx].set_xticklabels(class_names, rotation=45, ha='right', fontsize=TICK_LABEL_FONT_SIZE, family='Times New Roman')
                axes[idx].set_yticklabels(class_names, fontsize=TICK_LABEL_FONT_SIZE, family='Times New Roman')

                # 每个子图都显示x轴标签，只有最左侧显示y轴标签
                axes[idx].set_xlabel('Predicted', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')
                if idx == 0:  # 只在最左侧显示y轴标签
                    axes[idx].set_ylabel('Actual', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')

                # 取消数值标注
                # for i in range(len(class_names)):
                #     for j in range(len(class_names)):
                #         value = normalized_cm[i, j]
                #         if value > 0.1 or i == j:  # 只显示大于0.1的值或对角线值
                #             text_color = 'white' if value > 0.5 else 'black'
                #             axes[idx].text(j, i, f'{value:.2f}',
                #                          ha='center', va='center',
                #                          color=text_color, fontsize=ANNOTATION_FONT_SIZE, family='Times New Roman')
            else:
                # 如果没有该SNR的数据，显示空白
                axes[idx].text(0.5, 0.5, f'No data\nfor {snr}dB',
                             ha='center', va='center', transform=axes[idx].transAxes,
                             family='Times New Roman')
                axes[idx].set_title(model_name, fontsize=TITLE_FONT_SIZE, fontweight='bold', family='Times New Roman')
                # 即使没有数据也要显示坐标轴标签
                axes[idx].set_xlabel('Predict class', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')
                if idx == 0:  # 只在最左侧显示y轴标签
                    axes[idx].set_ylabel('Actual class', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')

        # 在最右边添加颜色条，去掉标签
        cbar_ax = fig.add_axes([COLORBAR_LEFT, COLORBAR_BOTTOM, COLORBAR_WIDTH, COLORBAR_HEIGHT])
        cbar = fig.colorbar(im, cax=cbar_ax)
        # 去掉颜色条标签，只保留刻度
        cbar.ax.tick_params(labelsize=COLORBAR_TICK_FONT_SIZE)
        for label in cbar.ax.get_yticklabels():
            label.set_family('Times New Roman')

        # 保存PNG和PDF文件
        png_path = os.path.join(output_dir, f'confusion_matrices_SNR_{snr}dB.png')
        pdf_path = os.path.join(output_dir, f'confusion_matrices_SNR_{snr}dB.pdf')

        plt.savefig(png_path, dpi=300, bbox_inches='tight')
        plt.savefig(pdf_path, bbox_inches='tight')  # PDF格式矢量图，不需要dpi参数

        print(f"已保存PNG: {png_path}")
        print(f"已保存PDF: {pdf_path}")

        plt.close()

def plot_combined_confusion_matrices(snr_values, output_dir):
    """绘制结合版本的混淆矩阵（垂直拼接多个SNR的完整图片）"""
    # 模型名称和对应的文件夹（按指定顺序：CLDNN AWN MAMC MAWDN MWRNN）
    from collections import OrderedDict
    models = OrderedDict([
        ('CLDNN', '混淆矩阵/cldnn'),
        ('AWN', '混淆矩阵/awn'),
        ('MAMC', '混淆矩阵/mamc'),
        ('MAWDN', '混淆矩阵/mawdn'),
        ('MWRNN', '混淆矩阵/mwrnn')
    ])

    # 加载所有模型的数据
    model_data = {}
    class_names = None

    for model_name, model_path in models.items():
        data = load_confusion_matrix_data(model_path)
        if data is not None:
            model_data[model_name] = data
            if class_names is None:
                class_names = data['metadata']['class_names']

    if not model_data:
        print("未找到任何有效的混淆矩阵数据")
        return

    # 创建垂直拼接的图：行数=SNR数量，列数=5个模型+颜色条空间
    num_snrs = len(snr_values)
    fig, axes = plt.subplots(num_snrs, 5, figsize=(FIGURE_WIDTH, FIGURE_HEIGHT * num_snrs))

    # 如果只有一行，确保axes是二维数组
    if num_snrs == 1:
        axes = axes.reshape(1, -1)

    # 调整子图间距，保持原有格式
    plt.subplots_adjust(left=SUBPLOT_LEFT, right=SUBPLOT_RIGHT,
                       bottom=SUBPLOT_BOTTOM, top=SUBPLOT_TOP,
                       wspace=SUBPLOT_WSPACE, hspace=0.5)

    # 为每个SNR创建一行完整的图
    for snr_idx, snr in enumerate(snr_values):
        for model_idx, (model_name, data) in enumerate(model_data.items()):
            snr_key = f'SNR_{snr}dB'

            if snr_key in data['confusion_matrices']:
                cm = data['confusion_matrices'][snr_key]['confusion_matrix']
                normalized_cm = normalize_confusion_matrix(cm)

                # 绘制热图，确保是正方形
                im = axes[snr_idx, model_idx].imshow(normalized_cm, cmap='Blues', aspect='equal', vmin=0, vmax=1)

                # 每行都显示模型名标题
                axes[snr_idx, model_idx].set_title(model_name, fontsize=TITLE_FONT_SIZE, fontweight='bold', family='Times New Roman')

                # 设置坐标轴标签 - 每个图都完整显示
                axes[snr_idx, model_idx].set_xticks(range(len(class_names)))
                axes[snr_idx, model_idx].set_yticks(range(len(class_names)))
                axes[snr_idx, model_idx].set_xticklabels(class_names, rotation=45, ha='right', fontsize=TICK_LABEL_FONT_SIZE, family='Times New Roman')
                axes[snr_idx, model_idx].set_yticklabels(class_names, fontsize=TICK_LABEL_FONT_SIZE, family='Times New Roman')

                # 每个子图都显示x轴标签，只有最左侧显示y轴标签
                axes[snr_idx, model_idx].set_xlabel('Predict class', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')
                if model_idx == 0:  # 只在最左侧显示y轴标签
                    axes[snr_idx, model_idx].set_ylabel('Actual class', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')

            else:
                # 如果没有该SNR的数据，显示空白
                axes[snr_idx, model_idx].text(0.5, 0.5, f'No data\nfor {snr}dB',
                                             ha='center', va='center', transform=axes[snr_idx, model_idx].transAxes,
                                             family='Times New Roman')
                axes[snr_idx, model_idx].set_title(model_name, fontsize=TITLE_FONT_SIZE, fontweight='bold', family='Times New Roman')
                axes[snr_idx, model_idx].set_xlabel('Predict class', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')
                if model_idx == 0:  # 只在最左侧显示y轴标签
                    axes[snr_idx, model_idx].set_ylabel('Actual class', fontsize=AXIS_LABEL_FONT_SIZE, family='Times New Roman')

        # 为每一行添加独立的颜色条，使用可调整的参数
        # 计算每行的高度比例
        row_height = 1.0 / num_snrs
        # 获取当前SNR对应的垂直偏移（如果超出列表长度，使用最后一个值）
        offset_idx = min(snr_idx, len(COLORBAR_VERTICAL_OFFSETS) - 1)
        vertical_offset = COLORBAR_VERTICAL_OFFSETS[offset_idx]
        # 计算当前行颜色条的位置
        cbar_bottom = SUBPLOT_BOTTOM + (num_snrs - 1 - snr_idx) * row_height + vertical_offset
        cbar_height = row_height * COMBINED_COLORBAR_HEIGHT_RATIO

        cbar_ax = fig.add_axes([COMBINED_COLORBAR_LEFT, cbar_bottom, COMBINED_COLORBAR_WIDTH, cbar_height])
        cbar = fig.colorbar(im, cax=cbar_ax)
        # 去掉颜色条标签，只保留刻度
        cbar.ax.tick_params(labelsize=COLORBAR_TICK_FONT_SIZE)
        for label in cbar.ax.get_yticklabels():
            label.set_family('Times New Roman')

    # 保存PNG和PDF文件
    png_path = os.path.join(output_dir, f'confusion_matrices_combined.png')
    pdf_path = os.path.join(output_dir, f'confusion_matrices_combined.pdf')

    plt.savefig(png_path, dpi=300, bbox_inches='tight')
    plt.savefig(pdf_path, bbox_inches='tight')

    print(f"已保存结合版PNG: {png_path}")
    print(f"已保存结合版PDF: {pdf_path}")

    plt.close()

def main():
    """主函数"""
    # 输出目录
    output_dir = '混淆矩阵'

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 绘制单独的混淆矩阵网格图
    plot_confusion_matrices_grid(SNR_VALUES, output_dir)

    # 如果启用，绘制结合版本
    if GENERATE_COMBINED:
        plot_combined_confusion_matrices(SNR_VALUES, output_dir)

    print("混淆矩阵可视化完成！")

if __name__ == "__main__":
    main()
