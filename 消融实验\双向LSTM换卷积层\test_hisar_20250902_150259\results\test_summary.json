{"overall_metrics": {"accuracy": 72.9523076923077, "macro_f1": 72.79963954425457, "kappa": 0.718704}, "model_complexity": {"macs": "N/A (请安装thop或ptflops)", "parameters": "N/A", "macs_raw": "N/A", "params_raw": "N/A"}, "inference_performance": {"avg_inference_time_ms": 0.08769721251267654, "std_inference_time_ms": 0.05987202333984377, "min_inference_time_ms": 0.06614066660404205, "max_inference_time_ms": 2.6248395442962646}, "dataset_info": {"total_samples": 260000, "dataset_type": "hisar", "input_shape": [2, 1024], "num_classes": 26, "snr_range": [-20.0, 18.0]}, "test_info": {"model_path": "./saved_models/wnn_mrnn/hisar_20250902_105108/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-09-02 15:04:34"}}