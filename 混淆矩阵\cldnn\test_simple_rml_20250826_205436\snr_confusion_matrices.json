{"metadata": {"class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "dataset_type": "rml", "total_samples": 66000, "snr_list": [-20.0, -18.0, -16.0, -14.0, -12.0, -10.0, -8.0, -6.0, -4.0, -2.0, 0.0, 2.0, 4.0, 6.0, 8.0, 10.0, 12.0, 14.0, 16.0, 18.0], "num_classes": 11}, "confusion_matrices": {"SNR_-20dB": {"snr": -20.0, "confusion_matrix": [[7, 8, 264, 8, 5, 2, 1, 1, 1, 3, 0], [4, 14, 246, 5, 10, 6, 2, 5, 0, 2, 6], [0, 12, 257, 3, 7, 5, 4, 3, 0, 4, 5], [5, 7, 261, 3, 6, 4, 1, 5, 0, 3, 5], [3, 11, 254, 2, 7, 11, 2, 0, 1, 5, 4], [5, 1, 278, 5, 1, 3, 3, 1, 0, 1, 2], [2, 9, 270, 3, 0, 6, 3, 2, 0, 2, 3], [3, 10, 268, 4, 3, 7, 1, 0, 0, 1, 3], [4, 7, 251, 5, 4, 5, 2, 2, 1, 10, 9], [0, 9, 269, 6, 5, 1, 0, 2, 2, 3, 3], [2, 10, 256, 4, 5, 7, 2, 4, 2, 6, 2]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.090909090909092, "macro_f1": 3.657644936314327, "kappa": -2.220446049250313e-16}, "sample_count": 3300}, "SNR_-18dB": {"snr": -18.0, "confusion_matrix": [[1, 10, 258, 3, 9, 4, 2, 0, 1, 3, 9], [2, 13, 253, 4, 6, 7, 3, 1, 0, 0, 11], [2, 8, 266, 4, 2, 3, 2, 2, 0, 7, 4], [2, 15, 248, 3, 7, 9, 2, 0, 3, 3, 8], [1, 6, 262, 7, 6, 3, 2, 6, 3, 1, 3], [3, 4, 260, 5, 5, 7, 3, 1, 0, 7, 5], [13, 13, 249, 3, 4, 5, 2, 2, 1, 3, 5], [7, 9, 253, 7, 4, 7, 1, 2, 3, 2, 5], [7, 9, 233, 10, 8, 11, 3, 6, 2, 6, 5], [2, 9, 264, 2, 2, 3, 3, 2, 3, 1, 9], [3, 8, 262, 1, 8, 5, 2, 4, 1, 1, 5]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.333333333333334, "macro_f1": 3.6412476892401417, "kappa": 0.002666666666666484}, "sample_count": 3300}, "SNR_-16dB": {"snr": -16.0, "confusion_matrix": [[2, 5, 278, 3, 4, 5, 0, 0, 1, 0, 2], [3, 20, 245, 6, 6, 3, 5, 2, 0, 3, 7], [4, 4, 265, 3, 6, 5, 3, 1, 2, 6, 1], [7, 7, 261, 1, 5, 10, 1, 1, 1, 3, 3], [4, 10, 253, 5, 8, 6, 2, 2, 1, 8, 1], [2, 5, 272, 4, 3, 3, 1, 2, 1, 3, 4], [6, 7, 256, 2, 3, 12, 2, 3, 0, 4, 5], [7, 6, 245, 8, 9, 4, 3, 2, 2, 7, 7], [23, 4, 150, 22, 20, 11, 10, 15, 12, 25, 8], [4, 5, 267, 4, 3, 8, 2, 1, 2, 2, 2], [7, 15, 248, 2, 3, 8, 0, 3, 2, 1, 11]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 9.93939393939394, "macro_f1": 4.773471854177897, "kappa": 0.009333333333333305}, "sample_count": 3300}, "SNR_-14dB": {"snr": -14.0, "confusion_matrix": [[6, 4, 258, 4, 10, 7, 3, 1, 0, 4, 3], [2, 31, 226, 3, 9, 3, 4, 0, 0, 3, 19], [4, 9, 260, 3, 4, 6, 6, 0, 0, 4, 4], [3, 7, 259, 4, 8, 8, 1, 2, 1, 4, 3], [4, 7, 253, 4, 7, 10, 1, 5, 1, 5, 3], [4, 10, 251, 5, 9, 9, 2, 2, 2, 2, 4], [10, 6, 224, 8, 20, 16, 6, 2, 0, 4, 4], [13, 6, 180, 14, 23, 20, 4, 16, 5, 15, 4], [48, 4, 28, 21, 27, 12, 18, 50, 41, 45, 6], [3, 4, 257, 4, 4, 8, 4, 2, 0, 5, 9], [2, 30, 229, 6, 1, 4, 3, 4, 1, 3, 17]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 12.181818181818182, "macro_f1": 8.501744701037527, "kappa": 0.03400000000000003}, "sample_count": 3300}, "SNR_-12dB": {"snr": -12.0, "confusion_matrix": [[5, 6, 259, 5, 8, 8, 2, 2, 1, 2, 2], [6, 72, 161, 3, 8, 7, 4, 0, 0, 2, 37], [6, 9, 251, 5, 4, 11, 4, 4, 1, 1, 4], [7, 16, 238, 7, 8, 7, 2, 6, 1, 3, 5], [6, 7, 255, 7, 7, 4, 4, 1, 3, 2, 4], [8, 15, 237, 6, 8, 12, 2, 0, 0, 3, 9], [14, 9, 176, 22, 17, 5, 24, 11, 4, 15, 3], [38, 5, 97, 28, 32, 11, 11, 21, 18, 35, 4], [53, 1, 2, 8, 15, 5, 6, 85, 103, 22, 0], [6, 12, 253, 6, 6, 6, 0, 4, 0, 5, 2], [2, 60, 183, 5, 6, 13, 4, 1, 2, 1, 23]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 16.060606060606062, "macro_f1": 13.5331082013564, "kappa": 0.07666666666666644}, "sample_count": 3300}, "SNR_-10dB": {"snr": -10.0, "confusion_matrix": [[16, 4, 231, 11, 12, 8, 2, 4, 2, 5, 5], [2, 118, 94, 1, 5, 19, 3, 2, 2, 1, 53], [6, 10, 263, 2, 6, 4, 4, 2, 1, 1, 1], [13, 4, 232, 12, 4, 10, 5, 7, 0, 6, 7], [7, 9, 223, 14, 11, 9, 5, 7, 2, 5, 8], [4, 25, 193, 5, 10, 32, 5, 5, 0, 3, 18], [21, 5, 48, 63, 9, 8, 102, 9, 16, 15, 4], [74, 1, 10, 15, 22, 17, 13, 54, 53, 37, 4], [31, 0, 2, 0, 1, 0, 2, 87, 166, 11, 0], [15, 6, 217, 7, 4, 9, 10, 9, 5, 14, 4], [5, 93, 112, 4, 7, 20, 2, 2, 0, 4, 51]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 25.424242424242426, "macro_f1": 23.447550260865665, "kappa": 0.17966666666666653}, "sample_count": 3300}, "SNR_-8dB": {"snr": -8.0, "confusion_matrix": [[31, 11, 135, 18, 26, 12, 14, 14, 14, 16, 9], [0, 157, 17, 2, 5, 25, 1, 3, 2, 2, 86], [5, 12, 260, 5, 3, 3, 3, 0, 3, 4, 2], [15, 4, 144, 45, 14, 7, 33, 15, 6, 16, 1], [20, 12, 114, 16, 50, 24, 16, 9, 11, 20, 8], [10, 29, 69, 8, 30, 101, 6, 9, 2, 8, 28], [7, 1, 5, 68, 4, 0, 201, 5, 8, 1, 0], [51, 0, 2, 3, 7, 0, 4, 75, 134, 24, 0], [6, 0, 2, 0, 0, 0, 0, 88, 202, 2, 0], [29, 8, 151, 13, 15, 18, 13, 17, 6, 27, 3], [2, 147, 15, 2, 4, 35, 1, 2, 0, 2, 90]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 37.54545454545454, "macro_f1": 34.776257828975595, "kappa": 0.31299999999999994}, "sample_count": 3300}, "SNR_-6dB": {"snr": -6.0, "confusion_matrix": [[56, 4, 33, 19, 31, 17, 20, 49, 30, 39, 2], [0, 185, 0, 0, 2, 16, 0, 0, 0, 0, 97], [6, 7, 263, 2, 4, 6, 2, 1, 1, 2, 6], [26, 3, 27, 129, 5, 4, 70, 20, 6, 8, 2], [33, 2, 22, 4, 149, 23, 9, 14, 12, 28, 4], [2, 23, 5, 2, 15, 203, 3, 2, 2, 3, 40], [0, 0, 3, 18, 0, 3, 270, 3, 3, 0, 0], [8, 0, 3, 0, 1, 0, 3, 105, 178, 2, 0], [4, 0, 3, 0, 0, 0, 0, 77, 216, 0, 0], [53, 5, 28, 26, 49, 9, 10, 43, 37, 37, 3], [0, 150, 0, 0, 0, 44, 0, 0, 0, 0, 106]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 52.090909090909086, "macro_f1": 49.77447986691293, "kappa": 0.473}, "sample_count": 3300}, "SNR_-4dB": {"snr": -4.0, "confusion_matrix": [[136, 1, 5, 6, 26, 1, 3, 48, 37, 37, 0], [0, 239, 0, 0, 0, 4, 0, 0, 0, 0, 57], [3, 8, 259, 3, 3, 10, 5, 2, 0, 5, 2], [6, 0, 2, 239, 0, 1, 40, 5, 3, 3, 1], [13, 1, 0, 1, 246, 12, 0, 6, 15, 6, 0], [0, 10, 1, 0, 8, 254, 0, 0, 2, 1, 24], [1, 0, 4, 5, 0, 0, 290, 0, 0, 0, 0], [7, 0, 5, 0, 0, 0, 0, 159, 129, 0, 0], [5, 0, 3, 0, 0, 0, 0, 71, 221, 0, 0], [103, 0, 5, 10, 22, 0, 2, 41, 37, 80, 0], [0, 186, 0, 0, 2, 28, 0, 0, 0, 0, 84]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 66.87878787878788, "macro_f1": 65.69234742117577, "kappa": 0.6356666666666666}, "sample_count": 3300}, "SNR_-2dB": {"snr": -2.0, "confusion_matrix": [[183, 0, 3, 3, 10, 0, 1, 36, 37, 27, 0], [0, 271, 0, 0, 0, 3, 0, 0, 0, 0, 26], [2, 5, 273, 4, 0, 4, 2, 1, 0, 4, 5], [0, 1, 4, 275, 0, 1, 17, 1, 0, 0, 1], [4, 0, 0, 0, 291, 2, 0, 1, 2, 0, 0], [0, 3, 0, 0, 0, 288, 0, 0, 0, 0, 9], [0, 0, 1, 2, 0, 0, 297, 0, 0, 0, 0], [3, 0, 2, 0, 0, 0, 0, 199, 96, 0, 0], [7, 0, 2, 0, 0, 0, 0, 83, 208, 0, 0], [60, 0, 3, 4, 3, 1, 1, 24, 19, 185, 0], [0, 198, 0, 0, 0, 22, 0, 0, 0, 0, 80]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 77.27272727272727, "macro_f1": 76.45615043750549, "kappa": 0.75}, "sample_count": 3300}, "SNR_0dB": {"snr": 0.0, "confusion_matrix": [[256, 0, 1, 0, 0, 0, 0, 15, 19, 8, 1], [0, 282, 0, 0, 0, 0, 0, 0, 0, 0, 18], [4, 4, 264, 3, 6, 8, 3, 1, 0, 0, 7], [0, 0, 4, 293, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 2, 0, 0, 0, 294, 0, 0, 0, 0, 4], [0, 0, 3, 1, 0, 0, 296, 0, 0, 0, 0], [7, 0, 3, 0, 0, 0, 0, 243, 45, 2, 0], [3, 0, 3, 0, 0, 0, 0, 68, 224, 1, 1], [21, 0, 3, 0, 1, 0, 0, 6, 2, 267, 0], [0, 191, 0, 0, 0, 7, 0, 0, 0, 0, 102]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 85.48484848484848, "macro_f1": 84.91055762590534, "kappa": 0.8403333333333333}, "sample_count": 3300}, "SNR_2dB": {"snr": 2.0, "confusion_matrix": [[286, 0, 4, 0, 0, 0, 1, 3, 3, 3, 0], [0, 281, 0, 0, 0, 0, 0, 0, 0, 0, 19], [5, 9, 265, 3, 6, 3, 3, 0, 0, 4, 2], [0, 0, 1, 299, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 1], [0, 0, 1, 1, 0, 0, 298, 0, 0, 0, 0], [6, 0, 3, 0, 0, 0, 0, 231, 60, 0, 0], [1, 0, 4, 0, 0, 0, 0, 80, 214, 1, 0], [2, 1, 1, 0, 0, 0, 0, 0, 0, 296, 0], [0, 173, 0, 0, 0, 1, 0, 0, 0, 0, 126]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 87.72727272727273, "macro_f1": 87.31544048536803, "kappa": 0.865}, "sample_count": 3300}, "SNR_4dB": {"snr": 4.0, "confusion_matrix": [[294, 0, 4, 0, 0, 0, 0, 0, 1, 1, 0], [0, 295, 0, 0, 0, 0, 0, 0, 0, 0, 5], [2, 9, 260, 3, 5, 6, 6, 2, 1, 3, 3], [0, 0, 2, 296, 0, 0, 1, 0, 0, 0, 1], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 1, 1, 0, 1, 0, 297, 0, 0, 0, 0], [2, 0, 3, 0, 0, 0, 1, 244, 50, 0, 0], [4, 0, 4, 0, 0, 0, 0, 76, 216, 0, 0], [0, 0, 3, 0, 0, 1, 0, 0, 0, 296, 0], [0, 183, 0, 0, 0, 1, 0, 0, 0, 0, 116]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 88.30303030303031, "macro_f1": 87.7403617435327, "kappa": 0.8713333333333333}, "sample_count": 3300}, "SNR_6dB": {"snr": 6.0, "confusion_matrix": [[297, 0, 0, 0, 2, 0, 0, 1, 0, 0, 0], [0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 1], [2, 7, 271, 4, 6, 4, 1, 0, 0, 3, 2], [0, 0, 1, 299, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 1], [0, 0, 1, 1, 0, 0, 298, 0, 0, 0, 0], [6, 0, 2, 0, 0, 0, 0, 244, 48, 0, 0], [7, 0, 1, 1, 0, 0, 0, 68, 223, 0, 0], [0, 0, 5, 0, 1, 0, 0, 0, 1, 293, 0], [0, 180, 0, 0, 0, 0, 0, 0, 0, 0, 120]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 89.18181818181819, "macro_f1": 88.63356696130714, "kappa": 0.881}, "sample_count": 3300}, "SNR_8dB": {"snr": 8.0, "confusion_matrix": [[295, 0, 3, 1, 0, 0, 1, 0, 0, 0, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 9, 266, 1, 3, 5, 3, 1, 1, 3, 3], [0, 0, 1, 298, 0, 0, 1, 0, 0, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 1], [0, 0, 2, 0, 0, 0, 298, 0, 0, 0, 0], [7, 0, 1, 1, 0, 0, 0, 245, 46, 0, 0], [5, 0, 2, 0, 0, 0, 0, 85, 208, 0, 0], [0, 1, 4, 0, 0, 0, 0, 0, 0, 295, 0], [0, 169, 0, 0, 0, 1, 0, 0, 0, 0, 130]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 88.9090909090909, "macro_f1": 88.4401613138354, "kappa": 0.878}, "sample_count": 3300}, "SNR_10dB": {"snr": 10.0, "confusion_matrix": [[296, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 8, 249, 8, 9, 5, 5, 1, 1, 4, 5], [0, 0, 1, 296, 0, 0, 2, 0, 0, 0, 1], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 2, 1, 0, 0, 295, 1, 0, 0, 1], [9, 0, 4, 0, 0, 0, 0, 245, 42, 0, 0], [3, 0, 2, 0, 1, 0, 1, 73, 220, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 300, 0], [0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 119]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 88.48484848484848, "macro_f1": 87.8977676007413, "kappa": 0.8733333333333333}, "sample_count": 3300}, "SNR_12dB": {"snr": 12.0, "confusion_matrix": [[293, 0, 3, 0, 1, 0, 0, 0, 0, 3, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 7, 266, 5, 2, 5, 1, 2, 2, 1, 5], [0, 0, 1, 298, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 1], [0, 0, 3, 0, 0, 0, 297, 0, 0, 0, 0], [7, 0, 1, 0, 0, 0, 0, 248, 44, 0, 0], [5, 0, 0, 0, 1, 0, 0, 85, 205, 4, 0], [0, 0, 1, 0, 1, 0, 0, 2, 0, 296, 0], [0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 122]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 88.60606060606061, "macro_f1": 88.06286726537903, "kappa": 0.8746666666666667}, "sample_count": 3300}, "SNR_14dB": {"snr": 14.0, "confusion_matrix": [[295, 0, 4, 0, 0, 0, 0, 0, 0, 1, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 6, 274, 0, 1, 6, 3, 1, 0, 3, 4], [0, 0, 5, 294, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 298, 0, 0, 0, 0, 2], [0, 0, 0, 1, 0, 0, 299, 0, 0, 0, 0], [7, 0, 4, 0, 0, 0, 0, 242, 47, 0, 0], [4, 0, 2, 0, 0, 0, 0, 79, 214, 1, 0], [2, 0, 3, 0, 1, 0, 0, 0, 0, 294, 0], [0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 130]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 89.0909090909091, "macro_f1": 88.62523063795298, "kappa": 0.88}, "sample_count": 3300}, "SNR_16dB": {"snr": 16.0, "confusion_matrix": [[292, 0, 1, 0, 0, 0, 0, 0, 3, 4, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 9, 260, 1, 3, 4, 4, 1, 2, 2, 11], [0, 0, 2, 297, 0, 0, 0, 0, 0, 0, 1], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0], [0, 0, 6, 0, 0, 0, 294, 0, 0, 0, 0], [9, 0, 1, 1, 0, 0, 0, 249, 40, 0, 0], [4, 0, 0, 1, 0, 0, 0, 73, 221, 0, 1], [0, 0, 3, 0, 0, 0, 0, 0, 1, 296, 0], [0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 124]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 88.87878787878788, "macro_f1": 88.39850597088504, "kappa": 0.8776666666666666}, "sample_count": 3300}, "SNR_18dB": {"snr": 18.0, "confusion_matrix": [[298, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0], [0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 6, 263, 6, 7, 5, 1, 0, 0, 5, 4], [0, 0, 1, 296, 0, 0, 2, 0, 0, 0, 1], [0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 1], [0, 0, 1, 1, 0, 0, 298, 0, 0, 0, 0], [5, 0, 3, 0, 0, 1, 0, 243, 46, 1, 1], [2, 0, 5, 0, 0, 0, 0, 65, 227, 0, 1], [0, 0, 5, 0, 0, 0, 0, 0, 2, 293, 0], [0, 174, 0, 0, 0, 1, 0, 0, 0, 0, 125]], "class_names": ["8PSK", "AM-DSB", "AM-SSB", "BPSK", "CPFSK", "GFSK", "PAM4", "QAM16", "QAM64", "QPSK", "WBFM"], "metrics": {"accuracy": 89.15151515151514, "macro_f1": 88.63747147421684, "kappa": 0.8806666666666667}, "sample_count": 3300}}}